#!/usr/bin/env python3
"""
Test the monthly processor with a single file to verify population calculations
"""

import pandas as pd
import re
import json
import os
import glob
import gzip
from math import radians, sin, cos, sqrt, atan2

# Function to calculate distance between two points using Haversine formula
def haversine_distance(lat1, lon1, lat2, lon2):
    # Convert latitude and longitude from degrees to radians
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])

    # Haversine formula
    dlon = lon2 - lon1
    dlat = lat2 - lat1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * atan2(sqrt(a), sqrt(1-a))
    r = 6371  # Radius of Earth in kilometers

    return r * c  # Distance in kilometers

# Function to parse visitor_home_cbgs with extra quotes
def parse_visitor_home_cbgs(json_str):
    if pd.isna(json_str) or json_str == '{}':
        return {}

    # Use regex to extract CBG and count pairs
    pattern = r'""""(\d+)"""":([\d]+)'
    matches = re.findall(pattern, str(json_str))

    result = {}
    for cbg, count in matches:
        result[cbg] = int(count)

    return result

# Function to get tract population from available data sources
def get_tract_population(tract_id):
    """
    Get the population of a tract from available data sources
    Returns None if population data is not available
    """
    # Try to get from census_population
    if tract_id in census_population:
        return census_population[tract_id]
    # If not found, try to get from tessellation_population
    elif tract_id in tessellation_population:
        return tessellation_population[tract_id]
    # If still not found, return None
    return None

# Function to load coordinate and population data with updated hierarchy
def load_coordinate_and_population_data():
    """
    Load coordinate and population data from all sources using updated 3-tier hierarchy:
    1. Census data (highest priority)
    2. Gazetteer data (medium priority)
    3. Tessellation data (lowest priority)

    Returns dictionaries for coordinates, populations, and coordinate sources
    """
    tract_coords = {}
    tract_populations = {}
    coord_sources = {}

    print("Loading coordinate and population data with updated hierarchy...")

    # Priority 1: Load from census data (highest priority)
    print("  Priority 1: Loading census data...")
    census_file = 'population files/census-populations-2020-tract-new-york.csv'
    if os.path.exists(census_file):
        census_df = pd.read_csv(census_file)
        census_count = 0
        for _, row in census_df.iterrows():
            # Keep tract ID format consistent with working processor - convert to string and remove .0 suffix
            tract_id = str(int(float(row['tract'])))
            if pd.notna(row['latitude']) and pd.notna(row['longitude']):
                tract_coords[tract_id] = {
                    'latitude': row['latitude'],
                    'longitude': row['longitude']
                }
                coord_sources[tract_id] = 'census'
                census_count += 1
            if pd.notna(row['population']):
                tract_populations[tract_id] = row['population']
        print(f"    Loaded {census_count} coordinates from census data")

    # Priority 2: Load from gazetteer data (medium priority - only if not in census)
    print("  Priority 2: Loading gazetteer data...")
    gaz_file = '2020_Gaz_tracts_national.txt'
    if os.path.exists(gaz_file):
        gazetteer_count = 0
        with open(gaz_file, 'r') as f:
            # Skip header
            next(f)
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 8:
                    tract_id = parts[1]
                    # Only add if not already from census data
                    if tract_id not in tract_coords:
                        try:
                            lat = float(parts[6])
                            lon = float(parts[7])
                            tract_coords[tract_id] = {
                                'latitude': lat,
                                'longitude': lon
                            }
                            coord_sources[tract_id] = 'gazetteer'
                            gazetteer_count += 1
                        except (ValueError, IndexError):
                            continue
        print(f"    Added {gazetteer_count} coordinates from gazetteer data")

    # Skip tessellation for this test
    print("  Priority 3: Skipping tessellation data for this test...")

    print(f"  Total coordinates loaded: {len(tract_coords)}")
    print(f"  Total population data loaded: {len(tract_populations)}")

    return tract_coords, tract_populations, coord_sources

# Create output directory if it doesn't exist
os.makedirs('monthly_output_files', exist_ok=True)

# Load coordinate and population data using the new function
tract_coords, tract_populations, coord_sources = load_coordinate_and_population_data()

# For backward compatibility, create separate dictionaries
census_coords = {k: v for k, v in tract_coords.items() if coord_sources.get(k) == 'census'}
gaz_coords = {k: v for k, v in tract_coords.items() if coord_sources.get(k) == 'gazetteer'}
tessellation_coords = {k: v for k, v in tract_coords.items() if coord_sources.get(k) == 'tessellation'}

# Create separate population dictionaries for backward compatibility
census_population = {}
tessellation_population = {}
for tract_id, population in tract_populations.items():
    source = coord_sources.get(tract_id, 'unknown')
    if source == 'census':
        census_population[tract_id] = population
    elif source == 'tessellation':
        tessellation_population[tract_id] = population

# Debug: Print population dictionary statistics
print(f"\nPopulation dictionary statistics:")
print(f"  Total tract_populations: {len(tract_populations)}")
print(f"  Census population dict: {len(census_population)} entries")
print(f"  Tessellation population dict: {len(tessellation_population)} entries")

# Get just the first input file for testing
input_files = glob.glob('NYC-2024-monthly-aggregated/*.csv.gz')
if input_files:
    input_file = input_files[0]  # Just process the first file
    print(f"\nTesting with file: {os.path.basename(input_file)}")
    
    # Extract the month from the filename
    file_basename = os.path.basename(input_file)
    date_match = re.search(r'NYC-2024-monthly-(\d{2})', file_basename)
    if date_match:
        month_str = date_match.group(1)
        date_str = f'2024-month-{month_str}'
    else:
        date_str = "test_month"

    output_file = f"monthly_output_files/tract_flows_with_population_{date_str}_test.csv"
    print(f"Output will be saved to: {output_file}")

    # Read the CSV file (just first 1000 rows for testing)
    print("Reading data (first 1000 rows for testing)...")
    try:
        # Read gzipped CSV file with UTF-8 encoding and error handling
        with gzip.open(input_file, 'rt', encoding='utf-8', errors='replace') as f:
            df = pd.read_csv(f, nrows=1000)  # Limit to 1000 rows for testing

        # Extract necessary columns and create a copy to avoid SettingWithCopyWarning
        print("Extracting columns...")
        data = df[['placekey', 'poi_cbg', 'visitor_home_cbgs', 'raw_visit_counts', 'latitude', 'longitude']].copy()

        # Create a list to store source-destination pairs at CBG level
        print("Processing visitor data...")
        cbg_flows = []

        # Process each row
        for _, row in data.iterrows():
            if pd.isna(row['poi_cbg']) or pd.isna(row['visitor_home_cbgs']):
                continue

            poi_cbg = str(row['poi_cbg'])  # Convert to string
            visitor_home_cbgs = parse_visitor_home_cbgs(row['visitor_home_cbgs'])

            # Create source-destination pairs
            for source_cbg, flow in visitor_home_cbgs.items():
                if pd.isna(row['latitude']) or pd.isna(row['longitude']):
                    continue

                cbg_flows.append({
                    'source_cbg': str(source_cbg),
                    'destination_cbg': poi_cbg,
                    'flow': flow,
                    'dest_lat': row['latitude'],
                    'dest_lon': row['longitude']
                })

        # Convert to DataFrame
        cbg_flow_df = pd.DataFrame(cbg_flows)

        # Check if we have any flows
        if len(cbg_flow_df) == 0:
            print("No valid flows found in the data. Exiting test.")
        else:
            print(f"Found {len(cbg_flow_df)} CBG flows")
            
            # Convert CBG to tract by taking the first 11 digits
            print("Aggregating to tract level...")
            cbg_flow_df['source_tract'] = cbg_flow_df['source_cbg'].astype(str).str[:11]
            cbg_flow_df['destination_tract'] = cbg_flow_df['destination_cbg'].astype(str).str[:11]

            # Aggregate flows to tract level
            tract_flow_df = cbg_flow_df.groupby(['source_tract', 'destination_tract']).agg({
                'flow': 'sum',
                'dest_lat': 'first',
                'dest_lon': 'first'
            }).reset_index()

            print(f"Aggregated to {len(tract_flow_df)} tract pairs")

            # Test population lookup for first few tract pairs
            print("\n🧪 Testing population lookup for first 5 tract pairs:")
            for idx, row in tract_flow_df.head().iterrows():
                source_tract = row['source_tract']
                dest_tract = row['destination_tract']
                
                source_pop = get_tract_population(source_tract)
                dest_pop = get_tract_population(dest_tract)
                
                print(f"  {idx}: {source_tract} -> {dest_tract}")
                print(f"    Source pop: {source_pop}, Dest pop: {dest_pop}")

            print(f"\n✅ Test completed successfully!")

    except Exception as e:
        print(f"Error processing file {input_file}: {e}")
else:
    print("No input files found!")
