#!/usr/bin/env python3
"""
Debug script to identify the tessellation population data inconsistency issue
in process_monthly_tract_flows.py

This script demonstrates the root cause of why tessellation population data
is not appearing in the final output despite being available in the dataset.
"""

import pandas as pd
import json
import os

def analyze_tessellation_population_issue():
    """
    Analyze the tessellation population data loading and separation logic
    to identify the root cause of missing population data in final output.
    """
    print("🔍 TESSELLATION POPULATION DATA INCONSISTENCY ANALYSIS")
    print("=" * 60)
    
    # Simulate the data loading process from process_monthly_tract_flows.py
    tract_coords = {}
    tract_populations = {}
    coord_sources = {}
    
    print("\n1. SIMULATING CENSUS DATA LOADING...")
    # Priority 1: Load census data (simulated)
    census_file = 'population files/census-populations-2020-tract-new-york.csv'
    if os.path.exists(census_file):
        census_df = pd.read_csv(census_file)
        census_count = 0
        for _, row in census_df.iterrows():
            tract_id = str(int(float(row['tract'])))
            if pd.notna(row['latitude']) and pd.notna(row['longitude']):
                tract_coords[tract_id] = {
                    'latitude': row['latitude'],
                    'longitude': row['longitude']
                }
                coord_sources[tract_id] = 'census'
                census_count += 1
            if pd.notna(row['population']):
                tract_populations[tract_id] = row['population']
        print(f"   ✓ Loaded {census_count} coordinates from census data")
        print(f"   ✓ Loaded {len([k for k, v in coord_sources.items() if v == 'census'])} census coordinate sources")
    
    print("\n2. SIMULATING GAZETTEER DATA LOADING...")
    # Priority 2: Load gazetteer data (simulated - coordinates only, NO population)
    gaz_file = '2020_Gaz_tracts_national.txt'
    if os.path.exists(gaz_file):
        gazetteer_count = 0
        with open(gaz_file, 'r') as f:
            next(f)  # Skip header
            for line_num, line in enumerate(f):
                if line_num >= 100:  # Limit for demo
                    break
                parts = line.strip().split('\t')
                if len(parts) >= 8:
                    tract_id = parts[1]
                    if tract_id not in tract_coords:
                        try:
                            lat = float(parts[6])
                            lon = float(parts[7])
                            tract_coords[tract_id] = {
                                'latitude': lat,
                                'longitude': lon
                            }
                            coord_sources[tract_id] = 'gazetteer'
                            gazetteer_count += 1
                        except (ValueError, IndexError):
                            continue
        print(f"   ✓ Added {gazetteer_count} coordinates from gazetteer data")
        print(f"   ⚠️  IMPORTANT: Gazetteer provides NO population data!")
    
    print("\n3. SIMULATING TESSELLATION DATA LOADING...")
    # Priority 3: Load tessellation data (this is where the bug occurs)
    tessellation_file = 'population files/tessellation_with_population.geojson'
    
    # 🚨 CRITICAL BUG IDENTIFIED: Line 135 in process_monthly_tract_flows.py
    print("   🚨 CRITICAL BUG FOUND: Line 135 in process_monthly_tract_flows.py")
    print("      if False and os.path.exists(tessellation_file):")
    print("      This condition is HARDCODED to False, completely skipping tessellation data!")
    
    if os.path.exists(tessellation_file):
        print(f"   ✓ Tessellation file exists: {tessellation_file}")
        print("   ❌ BUT tessellation loading is DISABLED by 'if False' condition")
        
        # Simulate what SHOULD happen if tessellation loading was enabled
        print("\n   📋 SIMULATING CORRECT TESSELLATION LOADING...")
        try:
            with open(tessellation_file, 'r') as f:
                tessellation_data = json.load(f)
            
            tessellation_populations_temp = {}
            temp_centroids = {}
            
            # Process first 10 features for demonstration
            for i, feature in enumerate(tessellation_data.get('features', [])[:10]):
                properties = feature.get('properties', {})
                tile_id = properties.get('tile_ID', '')
                
                if tile_id and len(str(tile_id)) >= 11:
                    tract_id = str(tile_id)[:11]
                    
                    # Collect population data (this is what's being skipped)
                    population = properties.get('population')
                    if population is not None and tract_id not in tract_populations:
                        tessellation_populations_temp[tract_id] = float(population)
                        print(f"      Found tessellation population: {tract_id} = {population}")
            
            print(f"   ✓ Would have loaded {len(tessellation_populations_temp)} tessellation populations")
            
        except Exception as e:
            print(f"   ❌ Error loading tessellation data: {e}")
    else:
        print(f"   ❌ Tessellation file not found: {tessellation_file}")
    
    print("\n4. ANALYZING DATA SEPARATION LOGIC...")
    # Simulate the separation process (lines 238-245)
    census_population = {}
    tessellation_population = {}
    
    for tract_id, population in tract_populations.items():
        source = coord_sources.get(tract_id, 'unknown')
        if source == 'census':
            census_population[tract_id] = population
        elif source == 'tessellation':
            tessellation_population[tract_id] = population
    
    print(f"   📊 Final population dictionaries:")
    print(f"      census_population: {len(census_population)} entries")
    print(f"      tessellation_population: {len(tessellation_population)} entries")
    
    print("\n5. ROOT CAUSE ANALYSIS SUMMARY:")
    print("   🎯 PRIMARY ISSUE: Line 135 - Tessellation loading is DISABLED")
    print("      if False and os.path.exists(tessellation_file):")
    print("      This hardcoded 'False' prevents ALL tessellation data from loading")
    
    print("\n   🔗 SECONDARY ISSUES:")
    print("      1. Population-coordinate dependency: Tessellation population data")
    print("         only gets added to tessellation_population dict if the tract")
    print("         has tessellation coordinates (coord_sources[tract_id] == 'tessellation')")
    print("      2. But tessellation coordinates are only added if tract is NOT")
    print("         already in tract_coords from census or gazetteer sources")
    print("      3. This creates a dependency where tessellation population data")
    print("         is lost for tracts that have coordinates from higher-priority sources")
    
    return census_population, tessellation_population

def demonstrate_fix():
    """
    Demonstrate how to fix the tessellation population issue
    """
    print("\n" + "=" * 60)
    print("🔧 PROPOSED FIX DEMONSTRATION")
    print("=" * 60)
    
    print("\n1. IMMEDIATE FIX: Enable tessellation loading")
    print("   Change line 135 from:")
    print("   if False and os.path.exists(tessellation_file):")
    print("   to:")
    print("   if os.path.exists(tessellation_file):")
    
    print("\n2. STRUCTURAL FIX: Decouple population from coordinates")
    print("   Modify tessellation loading to collect population data")
    print("   independently of coordinate availability:")
    print("""
   # Collect population data for ALL tracts, regardless of coordinate source
   for feature in tessellation_data.get('features', []):
       properties = feature.get('properties', {})
       tile_id = properties.get('tile_ID', '')
       
       if tile_id and len(str(tile_id)) >= 11:
           tract_id = str(tile_id)[:11]
           population = properties.get('population')
           
           # Add population if not already available from higher-priority source
           if population is not None and tract_id not in tract_populations:
               tract_populations[tract_id] = float(population)
               # Set coordinate source to 'tessellation' for population tracking
               if tract_id not in coord_sources:
                   coord_sources[tract_id] = 'tessellation'
    """)
    
    print("\n3. VALIDATION FIX: Add debugging output")
    print("   Add comprehensive logging to track population data flow")

if __name__ == "__main__":
    census_pop, tess_pop = analyze_tessellation_population_issue()
    demonstrate_fix()
    
    print("\n" + "=" * 60)
    print("🎯 CONCLUSION")
    print("=" * 60)
    print("The tessellation population data is not appearing in the final output because:")
    print("1. Tessellation data loading is completely DISABLED (line 135: 'if False')")
    print("2. Even if enabled, the current logic creates a coordinate-population dependency")
    print("3. This prevents tessellation population from being used for tracts with")
    print("   coordinates from higher-priority sources (census/gazetteer)")
    print("\nFix: Enable tessellation loading AND decouple population from coordinates")
