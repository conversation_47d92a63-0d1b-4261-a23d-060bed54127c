#!/usr/bin/env python3
"""
Quick verification script to check if tract 72139060403 exists in tessellation data
and trace its population value extraction
"""

import json
import os
import pandas as pd

def verify_tract_in_tessellation():
    """
    Verify if tract 72139060403 exists in tessellation data and extract its population
    """
    print("🔍 VERIFYING TRACT 72139060403 IN TESSELLATION DATA")
    print("=" * 60)
    
    target_tract = '72139060403'
    tessellation_file = 'population files/tessellation_with_population.geojson'
    
    if not os.path.exists(tessellation_file):
        print(f"❌ Tessellation file not found: {tessellation_file}")
        return False
    
    print(f"✅ Tessellation file exists: {tessellation_file}")
    print(f"📊 File size: {os.path.getsize(tessellation_file):,} bytes")
    
    try:
        print("\n🔄 Loading tessellation data...")
        with open(tessellation_file, 'r') as f:
            tessellation_data = json.load(f)
        
        features = tessellation_data.get('features', [])
        print(f"📊 Total features in tessellation data: {len(features):,}")
        
        # Search for the target tract
        found_features = []
        tract_populations = {}
        
        print(f"\n🔍 Searching for tract {target_tract}...")
        
        for i, feature in enumerate(features):
            if i % 10000 == 0:
                print(f"   Processed {i:,} features...")
            
            properties = feature.get('properties', {})
            tile_id = properties.get('tile_ID', '')
            
            if tile_id and len(str(tile_id)) >= 11:
                # Extract tract ID from tile_ID (first 11 digits)
                tract_id = str(tile_id)[:11]
                
                if tract_id == target_tract:
                    population = properties.get('population')
                    found_features.append({
                        'feature_index': i,
                        'tile_ID': tile_id,
                        'tract_id': tract_id,
                        'population': population,
                        'properties_keys': list(properties.keys())
                    })
                    
                    if population is not None:
                        tract_populations[tract_id] = float(population)
                    
                    print(f"🎯 FOUND: Feature {i}")
                    print(f"    tile_ID: {tile_id}")
                    print(f"    tract_id: {tract_id}")
                    print(f"    population: {population}")
                    print(f"    properties keys: {list(properties.keys())}")
        
        print(f"\n📊 SEARCH RESULTS:")
        print(f"   Features found for tract {target_tract}: {len(found_features)}")
        
        if found_features:
            print(f"✅ SUCCESS: Tract {target_tract} EXISTS in tessellation data")
            
            # Show all found instances
            for i, feature_info in enumerate(found_features):
                print(f"\n   Instance {i+1}:")
                print(f"     Feature index: {feature_info['feature_index']}")
                print(f"     tile_ID: {feature_info['tile_ID']}")
                print(f"     Population: {feature_info['population']}")
            
            # Check population extraction
            if target_tract in tract_populations:
                print(f"\n✅ POPULATION EXTRACTED: {tract_populations[target_tract]}")
                return True, tract_populations[target_tract]
            else:
                print(f"\n❌ POPULATION NOT EXTRACTED (None values)")
                return True, None
        else:
            print(f"❌ FAILURE: Tract {target_tract} NOT FOUND in tessellation data")
            return False, None
            
    except Exception as e:
        print(f"❌ Error loading tessellation data: {e}")
        return False, None

def verify_tract_format_consistency():
    """
    Verify tract ID format consistency across different data sources
    """
    print("\n" + "=" * 60)
    print("🔍 VERIFYING TRACT ID FORMAT CONSISTENCY")
    print("=" * 60)
    
    target_tract = '72139060403'
    
    # Check census data format
    print("\n1. Census data format:")
    census_file = 'population files/census-populations-2020-tract-new-york.csv'
    if os.path.exists(census_file):
        census_df = pd.read_csv(census_file)
        # Check if any tract IDs start with 721390604
        matching_census = census_df[census_df['tract'].astype(str).str.startswith('721390604')]
        print(f"   Census tracts starting with 721390604: {len(matching_census)}")
        if len(matching_census) > 0:
            print(f"   Sample census tract IDs: {matching_census['tract'].head().tolist()}")
    
    # Check flow data format (simulated)
    print("\n2. Flow data format (11-digit extraction):")
    test_cbgs = [
        '721390604031',  # 12-digit CBG
        '721390604032',  # 12-digit CBG  
        '721390604033',  # 12-digit CBG
    ]
    
    for cbg in test_cbgs:
        tract_from_cbg = str(cbg)[:11]
        print(f"   CBG {cbg} -> Tract {tract_from_cbg}")
        if tract_from_cbg == target_tract:
            print(f"     ✅ MATCH: This CBG would generate target tract {target_tract}")
    
    # Check tessellation tile_ID format
    print("\n3. Tessellation tile_ID format:")
    print(f"   Target tract: {target_tract}")
    print(f"   Expected tile_ID patterns:")
    print(f"     - Starts with {target_tract}")
    print(f"     - Length >= 11 characters")
    print(f"     - First 11 digits = {target_tract}")

def check_coordinate_sources():
    """
    Check if tract might have coordinates from higher-priority sources
    """
    print("\n" + "=" * 60)
    print("🔍 CHECKING COORDINATE SOURCES FOR TRACT 72139060403")
    print("=" * 60)
    
    target_tract = '72139060403'
    
    # Check census data
    print("\n1. Census data check:")
    census_file = 'population files/census-populations-2020-tract-new-york.csv'
    if os.path.exists(census_file):
        census_df = pd.read_csv(census_file)
        census_df['tract_str'] = census_df['tract'].astype(str).str.replace('.0', '')
        
        if target_tract in census_df['tract_str'].values:
            print(f"   ✅ Found {target_tract} in CENSUS data")
            tract_row = census_df[census_df['tract_str'] == target_tract].iloc[0]
            print(f"   Population: {tract_row['population']}")
            print(f"   Coordinates: ({tract_row['latitude']}, {tract_row['longitude']})")
            return 'census'
        else:
            print(f"   ❌ {target_tract} NOT found in census data")
    
    # Check gazetteer data
    print("\n2. Gazetteer data check:")
    gaz_file = '2020_Gaz_tracts_national.txt'
    if os.path.exists(gaz_file):
        found_in_gaz = False
        with open(gaz_file, 'r') as f:
            next(f)  # Skip header
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 8:
                    tract_id = parts[1]
                    if tract_id == target_tract:
                        print(f"   ✅ Found {target_tract} in GAZETTEER data")
                        print(f"   Coordinates: ({parts[6]}, {parts[7]})")
                        found_in_gaz = True
                        return 'gazetteer'
        
        if not found_in_gaz:
            print(f"   ❌ {target_tract} NOT found in gazetteer data")
    
    print(f"\n3. Expected coordinate source: TESSELLATION")
    print(f"   Since tract not found in census or gazetteer, it should use tessellation coordinates")
    print(f"   This means coord_sources['{target_tract}'] should be 'tessellation'")
    
    return 'tessellation'

if __name__ == "__main__":
    # Run verification
    found, population = verify_tract_in_tessellation()
    verify_tract_format_consistency()
    expected_source = check_coordinate_sources()
    
    print("\n" + "=" * 60)
    print("🎯 VERIFICATION SUMMARY")
    print("=" * 60)
    
    if found:
        print(f"✅ Tract 72139060403 EXISTS in tessellation data")
        if population is not None:
            print(f"✅ Population value: {population}")
            print(f"✅ Expected coordinate source: {expected_source}")
            print(f"✅ Should appear in tessellation_population dictionary")
            print(f"✅ Should be accessible via get_tract_population()")
        else:
            print(f"❌ Population value is None - check data quality")
    else:
        print(f"❌ Tract 72139060403 NOT FOUND in tessellation data")
        print(f"❌ This explains why it doesn't appear in final output")
    
    print(f"\n🔧 Next step: Run the main script with debugging to see detailed trace")
