#!/usr/bin/env python3
"""
Comprehensive fix for the tessellation population data issue in process_monthly_tract_flows.py

This script provides the corrected logic for loading tessellation population data
and demonstrates the proper separation of population and coordinate data.
"""

def fixed_load_coordinate_and_population_data():
    """
    CORRECTED version of load_coordinate_and_population_data() function
    that properly handles tessellation population data
    """
    import pandas as pd
    import json
    import os
    
    tract_coords = {}
    tract_populations = {}
    coord_sources = {}
    
    print("Loading coordinate and population data with FIXED hierarchy...")
    
    # Priority 1: Load from census data (highest priority)
    print("  Priority 1: Loading census data...")
    census_file = 'population files/census-populations-2020-tract-new-york.csv'
    if os.path.exists(census_file):
        census_df = pd.read_csv(census_file)
        census_count = 0
        for _, row in census_df.iterrows():
            tract_id = str(int(float(row['tract'])))
            if pd.notna(row['latitude']) and pd.notna(row['longitude']):
                tract_coords[tract_id] = {
                    'latitude': row['latitude'],
                    'longitude': row['longitude']
                }
                coord_sources[tract_id] = 'census'
                census_count += 1
            if pd.notna(row['population']):
                tract_populations[tract_id] = row['population']
        print(f"    Loaded {census_count} coordinates from census data")
    
    # Priority 2: Load from gazetteer data (medium priority - only if not in census)
    print("  Priority 2: Loading gazetteer data...")
    gaz_file = '2020_Gaz_tracts_national.txt'
    if os.path.exists(gaz_file):
        gazetteer_count = 0
        with open(gaz_file, 'r') as f:
            next(f)  # Skip header
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 8:
                    tract_id = parts[1]
                    if tract_id not in tract_coords:
                        try:
                            lat = float(parts[6])
                            lon = float(parts[7])
                            tract_coords[tract_id] = {
                                'latitude': lat,
                                'longitude': lon
                            }
                            coord_sources[tract_id] = 'gazetteer'
                            gazetteer_count += 1
                        except (ValueError, IndexError):
                            continue
        print(f"    Added {gazetteer_count} coordinates from gazetteer data")
    
    # Priority 3: Load tessellation data - FIXED VERSION
    print("  Priority 3: Loading tessellation data (FIXED)...")
    tessellation_file = 'population files/tessellation_with_population.geojson'
    
    # 🔧 FIX #1: Remove the hardcoded False condition
    if os.path.exists(tessellation_file):  # FIXED: Removed "False and"
        try:
            with open(tessellation_file, 'r') as f:
                tessellation_data = json.load(f)
            
            # Phase 1: Collect all polygon centroids per tract ID
            temp_centroids = {}
            tessellation_populations_temp = {}
            
            for feature in tessellation_data.get('features', []):
                properties = feature.get('properties', {})
                tile_id = properties.get('tile_ID', '')
                
                if tile_id and len(str(tile_id)) >= 11:
                    tract_id = str(tile_id)[:11]
                    
                    # Coordinate processing (only if not already found)
                    if tract_id not in tract_coords:
                        geometry = feature.get('geometry', {})
                        if geometry.get('type') == 'Polygon':
                            coords = geometry.get('coordinates', [])[0]
                            if coords and len(coords) > 0:
                                valid_coords = []
                                for coord in coords:
                                    if (len(coord) >= 2 and
                                        coord[0] is not None and coord[1] is not None and
                                        not pd.isna(coord[0]) and not pd.isna(coord[1]) and
                                        coord[0] != 0 and coord[1] != 0):
                                        valid_coords.append(coord)
                                
                                if valid_coords:
                                    sum_x = sum(p[0] for p in valid_coords)
                                    sum_y = sum(p[1] for p in valid_coords)
                                    count = len(valid_coords)
                                    centroid_lon = sum_x / count
                                    centroid_lat = sum_y / count
                                    
                                    if tract_id not in temp_centroids:
                                        temp_centroids[tract_id] = []
                                    temp_centroids[tract_id].append({
                                        'lat': centroid_lat,
                                        'lon': centroid_lon
                                    })
                    
                    # 🔧 FIX #2: Collect population data INDEPENDENTLY of coordinate availability
                    population = properties.get('population')
                    if population is not None and tract_id not in tract_populations:
                        tessellation_populations_temp[tract_id] = float(population)
                        # 🔧 FIX #3: Ensure coordinate source tracking for population-only tracts
                        if tract_id not in coord_sources:
                            coord_sources[tract_id] = 'tessellation'
            
            # Phase 2: Calculate averaged centroids for tracts with multiple polygons
            tessellation_count = 0
            for tract_id, centroid_list in temp_centroids.items():
                if len(centroid_list) > 1:
                    final_lat = sum(centroid['lat'] for centroid in centroid_list) / len(centroid_list)
                    final_lon = sum(centroid['lon'] for centroid in centroid_list) / len(centroid_list)
                else:
                    final_lat = centroid_list[0]['lat']
                    final_lon = centroid_list[0]['lon']
                
                if (-90 <= final_lat <= 90 and -180 <= final_lon <= 180):
                    tract_coords[tract_id] = {
                        'latitude': final_lat,
                        'longitude': final_lon
                    }
                    # Only update coordinate source if not already set
                    if tract_id not in coord_sources:
                        coord_sources[tract_id] = 'tessellation'
                    tessellation_count += 1
            
            # 🔧 FIX #4: Add tessellation population data with proper tracking
            tessellation_pop_added = 0
            for tract_id, population in tessellation_populations_temp.items():
                if tract_id not in tract_populations:
                    tract_populations[tract_id] = population
                    tessellation_pop_added += 1
            
            print(f"    Added {tessellation_count} coordinates from tessellation data")
            print(f"    Added {tessellation_pop_added} population values from tessellation data")
            print(f"    Processed {len(temp_centroids)} tracts with {sum(len(centroids) for centroids in temp_centroids.values())} total polygons")
            
        except Exception as e:
            print(f"    Error loading tessellation data: {e}")
    else:
        print(f"    Tessellation file not found: {tessellation_file}")
    
    print(f"  Total coordinates loaded: {len(tract_coords)}")
    print(f"  Total population data loaded: {len(tract_populations)}")
    
    return tract_coords, tract_populations, coord_sources

def fixed_population_separation(tract_populations, coord_sources):
    """
    CORRECTED version of population dictionary separation that properly
    handles tessellation population data
    """
    # 🔧 FIX #5: Enhanced separation logic with debugging
    census_population = {}
    tessellation_population = {}
    gazetteer_population = {}  # For completeness (though gazetteer has no population)
    
    for tract_id, population in tract_populations.items():
        source = coord_sources.get(tract_id, 'unknown')
        if source == 'census':
            census_population[tract_id] = population
        elif source == 'tessellation':
            tessellation_population[tract_id] = population
        elif source == 'gazetteer':
            gazetteer_population[tract_id] = population
        else:
            print(f"Warning: Unknown coordinate source '{source}' for tract {tract_id}")
    
    print(f"\n🔧 FIXED Population dictionary statistics:")
    print(f"  Total tract_populations: {len(tract_populations)}")
    print(f"  Census population dict: {len(census_population)} entries")
    print(f"  Tessellation population dict: {len(tessellation_population)} entries")
    print(f"  Gazetteer population dict: {len(gazetteer_population)} entries")
    
    return census_population, tessellation_population

def demonstrate_fix():
    """
    Demonstrate the complete fix for tessellation population data
    """
    print("🔧 DEMONSTRATING TESSELLATION POPULATION FIX")
    print("=" * 50)
    
    # Load data with fixed logic
    tract_coords, tract_populations, coord_sources = fixed_load_coordinate_and_population_data()
    
    # Separate populations with fixed logic
    census_pop, tessellation_pop = fixed_population_separation(tract_populations, coord_sources)
    
    # Show the improvement
    print(f"\n✅ RESULTS AFTER FIX:")
    print(f"   Tessellation population entries: {len(tessellation_pop)}")
    if tessellation_pop:
        sample_tracts = list(tessellation_pop.keys())[:5]
        print(f"   Sample tessellation tract IDs: {sample_tracts}")
        sample_pops = [tessellation_pop[tid] for tid in sample_tracts]
        print(f"   Sample tessellation populations: {sample_pops}")

if __name__ == "__main__":
    demonstrate_fix()
