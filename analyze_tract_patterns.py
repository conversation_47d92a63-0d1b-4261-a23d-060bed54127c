#!/usr/bin/env python3
"""
Analyze tract ID patterns in flow data to understand the root cause
of the tessellation population issue
"""

import pandas as pd
import gzip
import glob
import os

def analyze_flow_data_tracts():
    """
    Analyze tract ID patterns in the flow data
    """
    print("🔍 ANALYZING TRACT ID PATTERNS IN FLOW DATA")
    print("=" * 50)
    
    # Get input files
    input_files = glob.glob('NYC-2024-monthly-aggregated/*.csv.gz')
    if not input_files:
        print("❌ No input files found")
        return
    
    input_file = input_files[0]
    print(f"Analyzing: {os.path.basename(input_file)}")
    
    try:
        # Read first 1000 rows to analyze tract patterns
        with gzip.open(input_file, 'rt', encoding='utf-8', errors='replace') as f:
            df = pd.read_csv(f, nrows=1000)
        
        print(f"Loaded {len(df)} rows for analysis")
        print(f"Columns: {list(df.columns)}")
        
        # Extract CBGs and convert to tracts
        cbgs = []
        if 'poi_cbg' in df.columns:
            cbgs.extend(df['poi_cbg'].dropna().astype(str).tolist())
        
        print(f"Found {len(cbgs)} CBGs")
        
        # Convert CBGs to tract IDs (first 11 digits)
        tracts = [str(cbg)[:11] for cbg in cbgs if len(str(cbg)) >= 11]
        unique_tracts = list(set(tracts))
        
        print(f"Converted to {len(unique_tracts)} unique tracts")
        
        # Analyze tract ID patterns
        tract_prefixes = {}
        for tract in tracts:
            prefix = tract[:2]  # First 2 digits (state code)
            tract_prefixes[prefix] = tract_prefixes.get(prefix, 0) + 1
        
        print(f"\nTract ID patterns found:")
        for prefix, count in sorted(tract_prefixes.items()):
            if prefix == '36':
                state_name = 'New York'
            elif prefix == '72':
                state_name = 'Puerto Rico'
            else:
                state_name = 'Other'
            print(f"  {prefix}xxx: {count} tracts ({state_name})")
        
        # Check for specific tract
        target_tract = '72139060403'
        if target_tract in tracts:
            print(f"\n✅ Target tract {target_tract} FOUND in flow data")
        else:
            print(f"\n❌ Target tract {target_tract} NOT found in sample")
            # Show some Puerto Rico tracts if any
            pr_tracts = [t for t in unique_tracts if t.startswith('72')]
            if pr_tracts:
                print(f"Sample Puerto Rico tracts: {pr_tracts[:5]}")
        
        print(f"\nSummary:")
        print(f"  Total unique tracts in sample: {len(unique_tracts)}")
        print(f"  NYC tracts (36xxx): {len([t for t in unique_tracts if t.startswith('36')])}")
        print(f"  Puerto Rico tracts (72xxx): {len([t for t in unique_tracts if t.startswith('72')])}")
        
        return unique_tracts
        
    except Exception as e:
        print(f"❌ Error analyzing flow data: {e}")
        return []

def check_data_source_coverage():
    """
    Check which data sources cover Puerto Rico vs NYC tracts
    """
    print("\n" + "=" * 50)
    print("🔍 CHECKING DATA SOURCE COVERAGE")
    print("=" * 50)
    
    target_tract = '72139060403'
    
    # Check census data
    print("\n1. Census data coverage:")
    census_file = 'population files/census-populations-2020-tract-new-york.csv'
    if os.path.exists(census_file):
        census_df = pd.read_csv(census_file)
        ny_tracts = census_df['tract'].astype(str).str[:2].value_counts()
        print(f"   Census data state coverage: {dict(ny_tracts)}")
        
        if target_tract in census_df['tract'].astype(str).str.replace('.0', '').values:
            print(f"   ✅ {target_tract} found in census data")
        else:
            print(f"   ❌ {target_tract} NOT found in census data")
    
    # Check gazetteer data
    print("\n2. Gazetteer data coverage:")
    gaz_file = '2020_Gaz_tracts_national.txt'
    if os.path.exists(gaz_file):
        state_codes = {}
        pr_tracts = []
        with open(gaz_file, 'r') as f:
            next(f)  # Skip header
            for i, line in enumerate(f):
                if i > 10000:  # Limit for quick analysis
                    break
                parts = line.strip().split('\t')
                if len(parts) >= 8:
                    tract_id = parts[1]
                    state_code = tract_id[:2]
                    state_codes[state_code] = state_codes.get(state_code, 0) + 1
                    
                    if tract_id == target_tract:
                        print(f"   ✅ {target_tract} found in gazetteer data")
                        return True
                    
                    if state_code == '72':
                        pr_tracts.append(tract_id)
        
        print(f"   Gazetteer state coverage (sample): {dict(list(state_codes.items())[:10])}")
        if pr_tracts:
            print(f"   Sample Puerto Rico tracts in gazetteer: {pr_tracts[:5]}")
        print(f"   ❌ {target_tract} NOT found in gazetteer sample")
    
    # Check tessellation data coverage
    print("\n3. Tessellation data coverage:")
    tessellation_file = 'population files/tessellation_with_population.geojson'
    if os.path.exists(tessellation_file):
        print(f"   File exists, size: {os.path.getsize(tessellation_file):,} bytes")
        print(f"   Expected coverage: NYC area only (36xxx tracts)")
        print(f"   ❌ {target_tract} likely NOT in tessellation (Puerto Rico tract)")
    
    return False

def identify_root_cause():
    """
    Identify the root cause of the tessellation population issue
    """
    print("\n" + "=" * 50)
    print("🎯 ROOT CAUSE ANALYSIS")
    print("=" * 50)
    
    target_tract = '72139060403'
    
    print(f"\nAnalyzing tract {target_tract}:")
    print(f"  State code: 72 (Puerto Rico)")
    print(f"  County code: 139 (Vieques)")
    print(f"  Tract code: 060403")
    
    print(f"\nData source analysis:")
    print(f"  1. Census data: NYC-specific (36xxx tracts only)")
    print(f"  2. Gazetteer data: National coverage (includes 72xxx)")
    print(f"  3. Tessellation data: NYC-specific (36xxx tracts only)")
    
    print(f"\nRoot cause identified:")
    print(f"  ❌ Tract {target_tract} is a Puerto Rico tract")
    print(f"  ❌ NYC tessellation data does not include Puerto Rico")
    print(f"  ❌ Census population data is NYC-only")
    print(f"  ❌ Only gazetteer might have coordinates (but no population)")
    
    print(f"\nExpected behavior:")
    print(f"  1. Tract {target_tract} should get coordinates from gazetteer (if available)")
    print(f"  2. Population should remain None (no population data available)")
    print(f"  3. Final output should show None for Source_Population/Destination_Population")
    print(f"  4. This is CORRECT behavior - not a bug!")
    
    return True

def recommend_solution():
    """
    Recommend solution for proper testing
    """
    print("\n" + "=" * 50)
    print("🔧 RECOMMENDED SOLUTION")
    print("=" * 50)
    
    print("\nFor proper testing of tessellation population fixes:")
    print("  1. Use NYC tract IDs (36xxx) that exist in tessellation data")
    print("  2. Find tracts that have tessellation population but no census population")
    print("  3. Test with tracts that appear in flow data")
    
    print("\nSuggested test tract IDs:")
    print("  - Look for 36xxx tracts in tessellation data")
    print("  - Find ones missing from census data")
    print("  - Verify they appear in flow data as source/destination")
    
    print("\nDebugging steps:")
    print("  1. Run tessellation loading with NYC tract debugging")
    print("  2. Identify tessellation-only population tracts")
    print("  3. Test population lookup with those tracts")
    print("  4. Verify they appear in final output")

if __name__ == "__main__":
    # Run analysis
    tracts = analyze_flow_data_tracts()
    check_data_source_coverage()
    identify_root_cause()
    recommend_solution()
    
    print("\n" + "=" * 50)
    print("✅ ANALYSIS COMPLETE")
    print("=" * 50)
    print("The 'issue' with tract 72139060403 is actually EXPECTED BEHAVIOR")
    print("Puerto Rico tracts should not have tessellation population data")
    print("Need to test with NYC tracts (36xxx) for proper validation")
