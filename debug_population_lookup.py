#!/usr/bin/env python3
"""
Debug Population Lookup

This script debugs the exact population lookup issue by testing with real tract IDs.

Author: Augment Agent
Date: 2024
"""

import pandas as pd
import gzip

def test_population_lookup_detailed():
    """Test population lookup with detailed debugging"""
    print("🔍 DETAILED POPULATION LOOKUP DEBUG")
    print("=" * 50)
    
    # Load census data with the EXACT same logic as the main script
    print("📊 Loading census data with exact same logic...")
    census_file = 'population files/census-populations-2020-tract-new-york.csv'
    census_df = pd.read_csv(census_file)
    
    tract_populations = {}
    coord_sources = {}
    
    for _, row in census_df.iterrows():
        # Fix tract ID format - ensure it's a clean string without .0 suffix
        tract_id = str(int(float(row['tract'])))
        coord_sources[tract_id] = 'census'
        if pd.notna(row['population']):
            tract_populations[tract_id] = row['population']
    
    print(f"   ✅ Loaded {len(tract_populations)} population entries")
    
    # Create separate population dictionaries exactly like main script
    census_population = {}
    tessellation_population = {}
    for tract_id, population in tract_populations.items():
        source = coord_sources.get(tract_id, 'unknown')
        if source == 'census':
            census_population[tract_id] = population
        elif source == 'tessellation':
            tessellation_population[tract_id] = population
    
    print(f"   📊 Census population dict: {len(census_population)} entries")
    print(f"   📊 Tessellation population dict: {len(tessellation_population)} entries")
    
    # Test with actual tract IDs from input data
    print(f"\n🔍 Loading actual tract IDs from input data...")
    with gzip.open('NYC-2024-monthly-aggregated/NYC-2024-monthly-01.csv.gz', 'rt') as f:
        df = pd.read_csv(f, nrows=50)
    
    dest_tracts = df['poi_cbg'].astype(str).str[:11].unique()
    print(f"   📋 Sample destination tracts: {dest_tracts[:10].tolist()}")
    
    # Test the exact get_tract_population function
    def get_tract_population(tract_id):
        """Exact copy of the function from main script"""
        if tract_id in census_population:
            return census_population[tract_id]
        elif tract_id in tessellation_population:
            return tessellation_population[tract_id]
        return None
    
    print(f"\n🧪 Testing population lookup for destination tracts:")
    found_count = 0
    for tract_id in dest_tracts[:15]:
        population = get_tract_population(tract_id)
        if population is not None:
            print(f"   ✅ {tract_id}: {population}")
            found_count += 1
        else:
            print(f"   ❌ {tract_id}: NOT FOUND")
            
            # Debug: Check if tract exists in census data at all
            if tract_id in census_population:
                print(f"      🔍 Found in census_population: {census_population[tract_id]}")
            else:
                print(f"      🔍 Not in census_population")
                
                # Check if it exists in the original tract_populations
                if tract_id in tract_populations:
                    print(f"      🔍 Found in tract_populations: {tract_populations[tract_id]}")
                    print(f"      🔍 Coordinate source: {coord_sources.get(tract_id, 'unknown')}")
                else:
                    print(f"      🔍 Not in tract_populations either")
    
    print(f"\n📊 Population lookup results: {found_count}/{len(dest_tracts[:15])} found")
    
    # Additional debugging: Check if NYC tract IDs exist in census data
    print(f"\n🔍 Checking for NYC tract IDs in census data...")
    nyc_tracts_in_census = [tid for tid in census_population.keys() if tid.startswith('36061')]
    print(f"   📊 NYC tracts in census_population: {len(nyc_tracts_in_census)}")
    if nyc_tracts_in_census:
        print(f"   📋 Sample NYC tracts: {nyc_tracts_in_census[:10]}")
        
        # Test lookup for these known NYC tracts
        print(f"\n🧪 Testing lookup for known NYC tracts:")
        for tract_id in nyc_tracts_in_census[:5]:
            population = get_tract_population(tract_id)
            print(f"   ✅ {tract_id}: {population}")
    
    return found_count > 0

if __name__ == "__main__":
    success = test_population_lookup_detailed()
    if success:
        print(f"\n✅ Population lookup is working for some tracts!")
    else:
        print(f"\n❌ Population lookup is completely failing!")
