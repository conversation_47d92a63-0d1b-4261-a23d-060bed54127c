#!/usr/bin/env python3
"""
Comprehensive verification of tessellation population data extraction and matching logic
in process_monthly_tract_flows.py
"""

import json
import pandas as pd
import gzip
import glob
import os

def verify_tract_id_extraction():
    """
    Verify tract ID extraction from tessellation tile_ID field
    """
    print("🔍 VERIFYING TRACT ID EXTRACTION FROM TESSELLATION DATA")
    print("=" * 60)
    
    tessellation_file = 'population files/tessellation_with_population.geojson'
    
    if not os.path.exists(tessellation_file):
        print(f"❌ Tessellation file not found: {tessellation_file}")
        return {}
    
    print(f"✅ Loading tessellation data from: {tessellation_file}")
    print(f"📊 File size: {os.path.getsize(tessellation_file):,} bytes")
    
    try:
        with open(tessellation_file, 'r') as f:
            tessellation_data = json.load(f)
        
        features = tessellation_data.get('features', [])
        print(f"📊 Total features: {len(features):,}")
        
        # Analyze first 100 features for tract ID extraction patterns
        tract_extraction_examples = []
        population_associations = {}
        
        print(f"\n🔍 Analyzing tract ID extraction patterns (first 100 features):")
        
        for i, feature in enumerate(features[:100]):
            properties = feature.get('properties', {})
            tile_id = properties.get('tile_ID', '')
            population = properties.get('population')
            
            if tile_id:
                # Apply the same extraction logic as in the main script
                if len(str(tile_id)) >= 11:
                    tract_id = str(tile_id)[:11]  # First 11 digits
                    
                    tract_extraction_examples.append({
                        'feature_index': i,
                        'tile_ID': tile_id,
                        'tile_ID_length': len(str(tile_id)),
                        'extracted_tract_id': tract_id,
                        'population': population,
                        'population_type': type(population).__name__
                    })
                    
                    # Store population association
                    if population is not None:
                        if tract_id not in population_associations:
                            population_associations[tract_id] = []
                        population_associations[tract_id].append({
                            'tile_ID': tile_id,
                            'population': population
                        })
        
        # Display extraction examples
        print(f"\n📋 Tract ID Extraction Examples:")
        for i, example in enumerate(tract_extraction_examples[:10]):
            print(f"   Example {i+1}:")
            print(f"     tile_ID: {example['tile_ID']}")
            print(f"     tile_ID length: {example['tile_ID_length']}")
            print(f"     Extracted tract_id: {example['extracted_tract_id']}")
            print(f"     Population: {example['population']} ({example['population_type']})")
            print()
        
        # Analyze population associations
        print(f"📊 Population Association Analysis:")
        print(f"   Unique tract IDs with population: {len(population_associations)}")
        
        # Show examples of tract IDs with multiple tile_IDs
        multi_tile_tracts = {k: v for k, v in population_associations.items() if len(v) > 1}
        print(f"   Tract IDs with multiple tile_IDs: {len(multi_tile_tracts)}")
        
        if multi_tile_tracts:
            example_tract = list(multi_tile_tracts.keys())[0]
            print(f"\n   Example multi-tile tract: {example_tract}")
            for tile_info in multi_tile_tracts[example_tract][:3]:
                print(f"     tile_ID: {tile_info['tile_ID']}, population: {tile_info['population']}")
        
        return population_associations
        
    except Exception as e:
        print(f"❌ Error loading tessellation data: {e}")
        return {}

def verify_flow_data_tract_format():
    """
    Verify tract ID format in flow data (from CBG truncation)
    """
    print("\n" + "=" * 60)
    print("🔍 VERIFYING FLOW DATA TRACT ID FORMAT")
    print("=" * 60)
    
    # Get input files
    input_files = glob.glob('NYC-2024-monthly-aggregated/*.csv.gz')
    if not input_files:
        print("❌ No flow data files found")
        return []
    
    input_file = input_files[0]
    print(f"📊 Analyzing: {os.path.basename(input_file)}")
    
    try:
        # Read sample data
        with gzip.open(input_file, 'rt', encoding='utf-8', errors='replace') as f:
            df = pd.read_csv(f, nrows=1000)
        
        # Extract CBGs and convert to tracts
        cbgs = df['poi_cbg'].dropna().astype(str).tolist()
        
        # Apply the same tract extraction logic as in main script
        flow_tract_examples = []
        for cbg in cbgs[:20]:  # First 20 examples
            if len(str(cbg)) >= 11:
                tract_id = str(cbg)[:11]  # First 11 digits (same logic)
                flow_tract_examples.append({
                    'cbg': cbg,
                    'cbg_length': len(str(cbg)),
                    'extracted_tract_id': tract_id
                })
        
        print(f"\n📋 Flow Data Tract Extraction Examples:")
        for i, example in enumerate(flow_tract_examples[:10]):
            print(f"   Example {i+1}:")
            print(f"     CBG: {example['cbg']}")
            print(f"     CBG length: {example['cbg_length']}")
            print(f"     Extracted tract_id: {example['extracted_tract_id']}")
        
        # Get unique tract IDs from flow data
        all_tracts = [str(cbg)[:11] for cbg in cbgs if len(str(cbg)) >= 11]
        unique_flow_tracts = list(set(all_tracts))
        
        print(f"\n📊 Flow Data Tract Summary:")
        print(f"   Total CBGs processed: {len(cbgs)}")
        print(f"   Unique tract IDs: {len(unique_flow_tracts)}")
        print(f"   Sample tract IDs: {unique_flow_tracts[:10]}")
        
        return unique_flow_tracts
        
    except Exception as e:
        print(f"❌ Error analyzing flow data: {e}")
        return []

def verify_census_gazetteer_format():
    """
    Verify tract ID format in census and gazetteer data
    """
    print("\n" + "=" * 60)
    print("🔍 VERIFYING CENSUS AND GAZETTEER TRACT ID FORMAT")
    print("=" * 60)
    
    # Check census data format
    print("\n1. Census Data Format:")
    census_file = 'population files/census-populations-2020-tract-new-york.csv'
    census_tracts = []
    
    if os.path.exists(census_file):
        census_df = pd.read_csv(census_file)
        
        # Apply the same tract ID conversion as in main script
        census_tract_examples = []
        for _, row in census_df.head(10).iterrows():
            original_tract = row['tract']
            # Same conversion logic: str(int(float(row['tract'])))
            converted_tract = str(int(float(original_tract)))
            census_tract_examples.append({
                'original': original_tract,
                'converted': converted_tract,
                'length': len(converted_tract)
            })
        
        print(f"   📋 Census Tract Conversion Examples:")
        for i, example in enumerate(census_tract_examples):
            print(f"     Example {i+1}: {example['original']} -> {example['converted']} (length: {example['length']})")
        
        # Get all census tract IDs
        census_tracts = [str(int(float(tract))) for tract in census_df['tract']]
        print(f"   📊 Total census tracts: {len(census_tracts)}")
        print(f"   📊 Sample census tract IDs: {census_tracts[:10]}")
    
    # Check gazetteer data format
    print("\n2. Gazetteer Data Format:")
    gaz_file = '2020_Gaz_tracts_national.txt'
    gaz_tracts = []
    
    if os.path.exists(gaz_file):
        gaz_tract_examples = []
        with open(gaz_file, 'r') as f:
            next(f)  # Skip header
            for i, line in enumerate(f):
                if i >= 10:  # First 10 examples
                    break
                parts = line.strip().split('\t')
                if len(parts) >= 8:
                    tract_id = parts[1]  # Direct use, no conversion
                    gaz_tract_examples.append({
                        'tract_id': tract_id,
                        'length': len(tract_id)
                    })
                    gaz_tracts.append(tract_id)
        
        print(f"   📋 Gazetteer Tract Examples:")
        for i, example in enumerate(gaz_tract_examples):
            print(f"     Example {i+1}: {example['tract_id']} (length: {example['length']})")
        
        print(f"   📊 Sample gazetteer tract IDs: {gaz_tracts[:10]}")
    
    return census_tracts, gaz_tracts

def verify_data_matching_consistency(tessellation_pops, flow_tracts, census_tracts, gaz_tracts):
    """
    Verify consistency between different data source tract ID formats
    """
    print("\n" + "=" * 60)
    print("🔍 VERIFYING DATA MATCHING CONSISTENCY")
    print("=" * 60)
    
    # Convert to sets for intersection analysis
    tess_tract_set = set(tessellation_pops.keys()) if tessellation_pops else set()
    flow_tract_set = set(flow_tracts)
    census_tract_set = set(census_tracts)
    gaz_tract_set = set(gaz_tracts)
    
    print(f"\n📊 Data Source Tract Counts:")
    print(f"   Tessellation tracts: {len(tess_tract_set)}")
    print(f"   Flow data tracts: {len(flow_tract_set)}")
    print(f"   Census tracts: {len(census_tract_set)}")
    print(f"   Gazetteer tracts: {len(gaz_tract_set)}")
    
    # Check intersections
    print(f"\n🔗 Data Source Intersections:")
    
    if tess_tract_set and flow_tract_set:
        tess_flow_intersection = tess_tract_set.intersection(flow_tract_set)
        print(f"   Tessellation ∩ Flow data: {len(tess_flow_intersection)} tracts")
        if tess_flow_intersection:
            print(f"     Sample matching tracts: {list(tess_flow_intersection)[:5]}")
    
    if census_tract_set and flow_tract_set:
        census_flow_intersection = census_tract_set.intersection(flow_tract_set)
        print(f"   Census ∩ Flow data: {len(census_flow_intersection)} tracts")
        if census_flow_intersection:
            print(f"     Sample matching tracts: {list(census_flow_intersection)[:5]}")
    
    if tess_tract_set and census_tract_set:
        tess_census_intersection = tess_tract_set.intersection(census_tract_set)
        print(f"   Tessellation ∩ Census: {len(tess_census_intersection)} tracts")
        if tess_census_intersection:
            print(f"     Sample matching tracts: {list(tess_census_intersection)[:5]}")
    
    # Check for format inconsistencies
    print(f"\n⚠️  Format Consistency Checks:")
    
    # Check tract ID lengths
    if tess_tract_set:
        tess_lengths = [len(tract) for tract in list(tess_tract_set)[:100]]
        print(f"   Tessellation tract ID lengths: {set(tess_lengths)}")
    
    if flow_tract_set:
        flow_lengths = [len(tract) for tract in list(flow_tract_set)[:100]]
        print(f"   Flow data tract ID lengths: {set(flow_lengths)}")
    
    if census_tract_set:
        census_lengths = [len(tract) for tract in list(census_tract_set)[:100]]
        print(f"   Census tract ID lengths: {set(census_lengths)}")
    
    return {
        'tessellation_flow_matches': len(tess_flow_intersection) if tess_tract_set and flow_tract_set else 0,
        'census_flow_matches': len(census_flow_intersection) if census_tract_set and flow_tract_set else 0,
        'tessellation_census_matches': len(tess_census_intersection) if tess_tract_set and census_tract_set else 0
    }

def trace_specific_nyc_tract():
    """
    Trace data flow for a specific NYC tract through all data sources
    """
    print("\n" + "=" * 60)
    print("🔍 TRACING SPECIFIC NYC TRACT DATA FLOW")
    print("=" * 60)
    
    # Use a known NYC tract for tracing
    target_tract = '36061003700'  # Manhattan tract
    print(f"🎯 Tracing tract: {target_tract}")
    
    # Check in tessellation data
    print(f"\n1. Tessellation Data Check:")
    tessellation_file = 'population files/tessellation_with_population.geojson'
    
    if os.path.exists(tessellation_file):
        try:
            with open(tessellation_file, 'r') as f:
                tessellation_data = json.load(f)
            
            found_in_tessellation = False
            tessellation_matches = []
            
            for i, feature in enumerate(tessellation_data.get('features', [])[:1000]):  # Check first 1000
                properties = feature.get('properties', {})
                tile_id = properties.get('tile_ID', '')
                population = properties.get('population')
                
                if tile_id and len(str(tile_id)) >= 11:
                    extracted_tract = str(tile_id)[:11]
                    
                    if extracted_tract == target_tract:
                        found_in_tessellation = True
                        tessellation_matches.append({
                            'tile_ID': tile_id,
                            'population': population,
                            'feature_index': i
                        })
            
            if found_in_tessellation:
                print(f"   ✅ Found {target_tract} in tessellation data")
                print(f"   📊 Number of matching features: {len(tessellation_matches)}")
                for match in tessellation_matches[:3]:
                    print(f"     tile_ID: {match['tile_ID']}, population: {match['population']}")
            else:
                print(f"   ❌ {target_tract} NOT found in tessellation sample")
                
        except Exception as e:
            print(f"   ❌ Error checking tessellation: {e}")
    
    # Check in census data
    print(f"\n2. Census Data Check:")
    census_file = 'population files/census-populations-2020-tract-new-york.csv'
    
    if os.path.exists(census_file):
        census_df = pd.read_csv(census_file)
        census_df['tract_str'] = census_df['tract'].apply(lambda x: str(int(float(x))))
        
        if target_tract in census_df['tract_str'].values:
            print(f"   ✅ Found {target_tract} in census data")
            tract_row = census_df[census_df['tract_str'] == target_tract].iloc[0]
            print(f"     Population: {tract_row['population']}")
            print(f"     Coordinates: ({tract_row['latitude']}, {tract_row['longitude']})")
        else:
            print(f"   ❌ {target_tract} NOT found in census data")
    
    print(f"\n3. Expected Data Flow for {target_tract}:")
    print(f"   - Should be extracted from tessellation tile_ID using [:11]")
    print(f"   - Should be associated with tessellation population value")
    print(f"   - Should match tract IDs from flow data CBG truncation")
    print(f"   - Should be accessible via get_tract_population() function")

if __name__ == "__main__":
    print("🔍 COMPREHENSIVE TESSELLATION DATA MATCHING VERIFICATION")
    print("=" * 70)
    
    # Run all verification steps
    tessellation_pops = verify_tract_id_extraction()
    flow_tracts = verify_flow_data_tract_format()
    census_tracts, gaz_tracts = verify_census_gazetteer_format()
    
    matching_stats = verify_data_matching_consistency(
        tessellation_pops, flow_tracts, census_tracts, gaz_tracts
    )
    
    trace_specific_nyc_tract()
    
    # Final summary
    print("\n" + "=" * 70)
    print("🎯 VERIFICATION SUMMARY")
    print("=" * 70)
    
    print(f"\n✅ Tract ID Extraction Logic Verified:")
    print(f"   - Tessellation: str(tile_id)[:11] ✅")
    print(f"   - Flow data: str(cbg)[:11] ✅")
    print(f"   - Census: str(int(float(tract))) ✅")
    print(f"   - Gazetteer: direct use ✅")
    
    print(f"\n📊 Data Matching Results:")
    print(f"   - Tessellation-Flow matches: {matching_stats.get('tessellation_flow_matches', 0)}")
    print(f"   - Census-Flow matches: {matching_stats.get('census_flow_matches', 0)}")
    print(f"   - Tessellation-Census matches: {matching_stats.get('tessellation_census_matches', 0)}")
    
    if matching_stats.get('tessellation_flow_matches', 0) > 0:
        print(f"\n✅ SUCCESS: Tract ID formats are CONSISTENT across data sources")
        print(f"✅ Tessellation population extraction should work correctly")
    else:
        print(f"\n❌ WARNING: Limited or no tract ID matches found")
        print(f"❌ May indicate format inconsistencies or data coverage issues")
